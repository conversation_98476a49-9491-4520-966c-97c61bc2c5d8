# Core Concepts

This chapter provides an in-depth introduction to the core concepts and design philosophy of Micro-Core, helping you better understand and use this micro-frontend framework.

## Micro-Frontend Architecture Overview

Micro-frontend is an architectural pattern that breaks down monolithic frontend applications into multiple independent, deployable small applications. Each micro-application can be developed, tested, and deployed by different teams using different technology stacks.

### Traditional Monolithic vs Micro-Frontend Applications

```
Traditional Monolithic Application:
┌─────────────────────────────────────┐
│        Monolithic Frontend          │
│  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐   │
│  │Mod A│ │Mod B│ │Mod C│ │Mod D│   │
│  └─────┘ └─────┘ └─────┘ └─────┘   │
└─────────────────────────────────────┘

Micro-Frontend Application:
┌─────────────────────────────────────┐
│            Main Application         │
├─────────────────────────────────────┤
│ ┌─────┐   ┌─────┐   ┌─────┐        │
│ │App A│   │App B│   │App C│        │
│ │React│   │ Vue │   │Angular│      │
│ └─────┘   └─────┘   └─────┘        │
└─────────────────────────────────────┘
```

## Micro-Core Architecture Design

Micro-Core adopts a micro-kernel architecture design, dividing the system into core and extension layers:

### Architecture Layer Diagram

```
┌─────────────────────────────────────────────────────────┐
│                Application Layer                        │
├─────────────────────────────────────────────────────────┤
│  React App  │  Vue App   │ Angular App │  Vanilla JS   │
├─────────────────────────────────────────────────────────┤
│                  Adapter Layer                          │
├─────────────────────────────────────────────────────────┤
│ React Adapter │ Vue Adapter │ Angular Adapter │ ...    │
├─────────────────────────────────────────────────────────┤
│                   Plugin Layer                          │
├─────────────────────────────────────────────────────────┤
│ Router Plugin │ Auth Plugin │ State Plugin │ ...       │
├─────────────────────────────────────────────────────────┤
│                    Core Layer                           │
├─────────────────────────────────────────────────────────┤
│ App Registry │ Lifecycle │ Event Bus │ Sandbox │ ...   │
└─────────────────────────────────────────────────────────┘
```

### Core Components

#### 1. App Registry

Manages registration, discovery, and lifecycle of all micro-applications:

```typescript
interface AppRegistry {
  // Register application
  register(app: AppConfig): void
  
  // Unregister application
  unregister(name: string): void
  
  // Get application info
  getApp(name: string): AppInfo | null
  
  // Get all applications
  getApps(): AppInfo[]
  
  // Get active applications
  getActiveApps(): AppInfo[]
}
```

#### 2. Lifecycle Manager

Manages the complete lifecycle of micro-applications:

```typescript
interface LifecycleManager {
  // Load application
  load(app: AppInfo): Promise<void>
  
  // Bootstrap application
  bootstrap(app: AppInfo): Promise<void>
  
  // Mount application
  mount(app: AppInfo): Promise<void>
  
  // Unmount application
  unmount(app: AppInfo): Promise<void>
  
  // Update application
  update(app: AppInfo): Promise<void>
}
```

#### 3. Event Bus

Provides inter-application communication mechanism:

```typescript
interface EventBus {
  // Emit event
  emit(event: string, data?: any): void
  
  // Listen to event
  on(event: string, handler: Function): () => void
  
  // Listen to event once
  once(event: string, handler: Function): () => void
  
  // Remove listener
  off(event: string, handler?: Function): void
  
  // Clear all listeners
  clear(): void
}
```

#### 4. Sandbox System

Provides application isolation mechanism:

```typescript
interface Sandbox {
  // Create sandbox
  create(app: AppInfo): SandboxInstance
  
  // Activate sandbox
  activate(sandbox: SandboxInstance): void
  
  // Deactivate sandbox
  deactivate(sandbox: SandboxInstance): void
  
  // Destroy sandbox
  destroy(sandbox: SandboxInstance): void
}
```

## Key Concepts Explained

### 1. Micro Application

A micro-application is an independent frontend application with the following characteristics:

- **Independent Development**: Can use different technology stacks
- **Independent Deployment**: Can be deployed and updated separately
- **Independent Operation**: Can run independently from the main application
- **Lifecycle**: Has complete lifecycle management

```typescript
interface MicroApp {
  name: string              // Application name
  entry: string            // Application entry
  container: string        // Mount container
  activeWhen: string | Function  // Activation condition
  props?: object           // Properties passed to application
  loader?: Function        // Custom loader
}
```

### 2. Application Lifecycle

Each micro-application has a complete lifecycle:

```
┌─────────┐    ┌─────────┐    ┌─────────┐    ┌─────────┐
│  LOAD   │───▶│BOOTSTRAP│───▶│  MOUNT  │───▶│ ACTIVE  │
└─────────┘    └─────────┘    └─────────┘    └─────────┘
                                                  │
┌─────────┐    ┌─────────┐    ┌─────────┐       │
│DESTROYED│◀───│ UNMOUNT │◀───│INACTIVE │◀──────┘
└─────────┘    └─────────┘    └─────────┘
```

#### Lifecycle Stages Explained

1. **LOAD**: Load application resources
2. **BOOTSTRAP**: Initialize application
3. **MOUNT**: Mount application to DOM
4. **ACTIVE**: Application is in active state
5. **INACTIVE**: Application is in inactive state
6. **UNMOUNT**: Unmount application from DOM
7. **DESTROYED**: Destroy application instance

### 3. Routing System

Micro-Core provides a flexible routing system:

#### Route Matching Rules

```typescript
// String matching
activeWhen: '/app1'

// Regular expression matching
activeWhen: /^\/app1/

// Function matching
activeWhen: (location) => location.pathname.startsWith('/app1')

// Array matching (multiple conditions)
activeWhen: ['/app1', '/app1/*']
```

#### Routing Modes

```typescript
const microCore = new MicroCore({
  router: {
    mode: 'history',    // 'history' | 'hash'
    base: '/',          // Base path
    strict: false,      // Strict mode
    sensitive: false    // Case sensitive
  }
})
```

### 4. Sandbox Isolation

Micro-Core provides multiple sandbox isolation strategies:

#### JavaScript Sandbox

```typescript
// Proxy sandbox (recommended)
sandbox: {
  type: 'proxy',
  strict: true
}

// Snapshot sandbox
sandbox: {
  type: 'snapshot'
}

// No sandbox
sandbox: {
  type: 'none'
}
```

#### CSS Sandbox

```typescript
// Style isolation configuration
sandbox: {
  css: {
    enabled: true,
    mode: 'scoped',     // 'scoped' | 'shadow-dom'
    prefix: 'micro-'    // CSS prefix
  }
}
```

### 5. Inter-Application Communication

#### Event Communication

```typescript
// Emit event
microCore.eventBus.emit('user-login', { userId: 123 })

// Listen to event
const unsubscribe = microCore.eventBus.on('user-login', (data) => {
  console.log('User login:', data)
})

// Unsubscribe
unsubscribe()
```

#### State Sharing

```typescript
// Set global state
microCore.setGlobalState({
  user: { name: 'John', role: 'admin' },
  theme: 'dark'
})

// Get global state
const user = microCore.getGlobalState('user')

// Listen to state changes
microCore.onGlobalStateChange((state, prevState) => {
  console.log('State changed:', state, prevState)
})
```

#### Props Passing

```typescript
// Pass props when registering application
microCore.registerApp({
  name: 'child-app',
  entry: 'http://localhost:3001',
  container: '#container',
  activeWhen: '/child',
  props: {
    user: { name: 'John' },
    theme: 'dark'
  }
})
```

### 6. Adapter System

Adapters are used to adapt different frontend frameworks:

#### React Adapter

```typescript
import { ReactAdapter } from '@micro-core/adapter-react'

const microCore = new MicroCore({
  adapters: [new ReactAdapter()]
})
```

#### Vue Adapter

```typescript
import { VueAdapter } from '@micro-core/adapter-vue'

const microCore = new MicroCore({
  adapters: [new VueAdapter()]
})
```

#### Custom Adapter

```typescript
class CustomAdapter implements Adapter {
  name = 'custom'
  
  async load(app: AppInfo): Promise<any> {
    // Application loading logic
  }
  
  async mount(app: AppInfo, props: any): Promise<void> {
    // Application mounting logic
  }
  
  async unmount(app: AppInfo): Promise<void> {
    // Application unmounting logic
  }
}
```

### 7. Plugin System

Plugins are used to extend Micro-Core functionality:

#### Built-in Plugins

```typescript
import { RouterPlugin, AuthPlugin } from '@micro-core/plugins'

const microCore = new MicroCore({
  plugins: [
    new RouterPlugin(),
    new AuthPlugin({
      loginUrl: '/login',
      tokenKey: 'access_token'
    })
  ]
})
```

#### Custom Plugin

```typescript
class CustomPlugin implements Plugin {
  name = 'custom-plugin'
  
  install(microCore: MicroCore): void {
    // Plugin installation logic
    microCore.eventBus.on('app:mounted', (app) => {
      console.log(`Application ${app.name} mounted`)
    })
  }
  
  uninstall(microCore: MicroCore): void {
    // Plugin uninstallation logic
  }
}
```

## Design Principles

### 1. Technology Stack Agnostic

Micro-Core doesn't restrict the technology stack used by micro-applications, supporting:

- React, Vue, Angular and other mainstream frameworks
- jQuery, Vanilla JS and other traditional technologies
- Any frontend technology that can run in browsers

### 2. Independent Deployment

Each micro-application can:

- Be developed and tested independently
- Be deployed and updated independently
- Have independent rollback and version management
- Have independent CI/CD processes

### 3. Runtime Integration

Micro-Core adopts runtime integration approach:

- Applications are dynamically loaded in browsers
- Supports hot-swapping of applications
- Supports on-demand loading of applications
- Supports preloading of applications

### 4. Isolation

Provides comprehensive isolation mechanisms:

- JavaScript execution environment isolation
- CSS style isolation
- DOM operation isolation
- Global variable isolation

### 5. Communication Mechanisms

Provides multiple communication methods:

- Event bus communication
- Global state sharing
- Props passing
- URL parameter passing
- LocalStorage/SessionStorage

## Best Practices

### 1. Application Splitting Principles

- **Clear Business Boundaries**: Split by business modules
- **Clear Team Boundaries**: Split by team responsibilities
- **Clear Technical Boundaries**: Split by technology stacks
- **Clear Deployment Boundaries**: Split by deployment frequency

### 2. Communication Design

- **Minimize Communication**: Reduce coupling between applications
- **Standardize Interfaces**: Define unified communication protocols
- **Asynchronous Communication**: Use asynchronous communication methods
- **Error Handling**: Comprehensive error handling mechanisms

### 3. State Management

- **Local State**: Applications manage their internal state
- **Shared State**: Share through global state manager
- **Persistent State**: Use persistent storage
- **State Synchronization**: Ensure state consistency

### 4. Performance Optimization

- **On-Demand Loading**: Only load currently needed applications
- **Preloading**: Preload potentially accessed applications
- **Caching Strategy**: Reasonable caching strategies
- **Resource Sharing**: Share common dependencies

## Summary

Micro-Core provides a complete solution for building modern micro-frontend applications through micro-kernel architecture, comprehensive lifecycle management, flexible routing system, powerful sandbox isolation, and rich communication mechanisms.

Understanding these core concepts will help you better design and implement micro-frontend architecture, building maintainable and scalable large-scale frontend applications.

## Next Steps

- [Application Management](./features/app-management.md) - Learn how to manage micro-applications
- [Routing System](./features/routing.md) - Deep dive into routing configuration
- [Sandbox Isolation](./features/sandbox.md) - Master application isolation mechanisms
- [Application Communication](./features/communication.md) - Implement inter-application communication
- [Plugin Development](./advanced/plugins.md) - Develop custom plugins