# Getting Started

This guide will help you get up and running with Micro-Core micro-frontend framework in 5 minutes.

## Prerequisites

- Node.js >= 16.0.0
- npm >= 7.0.0 or yarn >= 1.22.0 or pnpm >= 6.0.0

## Installation

### Core Package Installation

::: code-group

```bash [npm]
npm install @micro-core/core
```

```bash [yarn]
yarn add @micro-core/core
```

```bash [pnpm]
pnpm add @micro-core/core
```

:::

### Adapter Installation (Optional)

Choose the appropriate adapter based on your tech stack:

::: code-group

```bash [React]
npm install @micro-core/adapter-react
```

```bash [Vue]
npm install @micro-core/adapter-vue
```

```bash [Angular]
npm install @micro-core/adapter-angular
```

:::

## Basic Usage

### 1. Create Main Application

Initialize Micro-Core in your main application:

```typescript
// main.ts
import { MicroCore } from '@micro-core/core'
import { ReactAdapter } from '@micro-core/adapter-react'

// Create micro-frontend instance
const microCore = new MicroCore({
  container: '#app',
  adapters: [new ReactAdapter()]
})

// Register sub-applications
microCore.registerApp({
  name: 'react-app',
  entry: 'http://localhost:3001',
  container: '#react-container',
  activeWhen: '/react'
})

microCore.registerApp({
  name: 'vue-app', 
  entry: 'http://localhost:3002',
  container: '#vue-container',
  activeWhen: '/vue'
})

// Start micro-frontend
microCore.start()
```

### 2. Configure Sub-Applications

#### React Sub-Application Configuration

```typescript
// src/index.tsx
import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'

// Micro-frontend lifecycle functions
export async function bootstrap() {
  console.log('React app bootstrapped')
}

export async function mount(props: any) {
  const container = props.container || document.getElementById('root')
  const root = ReactDOM.createRoot(container)
  root.render(<App />)
}

export async function unmount(props: any) {
  const container = props.container || document.getElementById('root')
  const root = ReactDOM.createRoot(container)
  root.unmount()
}

// Logic for standalone operation
if (!window.__MICRO_CORE__) {
  mount({})
}
```

#### Vue Sub-Application Configuration

```typescript
// src/main.ts
import { createApp } from 'vue'
import App from './App.vue'

let app: any = null

// Micro-frontend lifecycle functions
export async function bootstrap() {
  console.log('Vue app bootstrapped')
}

export async function mount(props: any) {
  app = createApp(App)
  const container = props.container || '#app'
  app.mount(container)
}

export async function unmount() {
  if (app) {
    app.unmount()
    app = null
  }
}

// Logic for standalone operation
if (!window.__MICRO_CORE__) {
  mount({})
}
```

### 3. Configure Build Tools

#### Vite Configuration

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import { microCorePlugin } from '@micro-core/builder-vite'

export default defineConfig({
  plugins: [
    microCorePlugin({
      name: 'react-app',
      entry: './src/index.tsx',
      exposes: {
        './App': './src/App.tsx'
      }
    })
  ],
  build: {
    lib: {
      entry: './src/index.tsx',
      name: 'ReactApp',
      formats: ['umd']
    }
  }
})
```

#### Webpack Configuration

```javascript
// webpack.config.js
const { MicroCorePlugin } = require('@micro-core/builder-webpack')

module.exports = {
  plugins: [
    new MicroCorePlugin({
      name: 'react-app',
      entry: './src/index.tsx',
      exposes: {
        './App': './src/App.tsx'
      }
    })
  ]
}
```

## Routing Configuration

### Main Application Routing

```typescript
import { createBrowserRouter } from 'react-router-dom'

const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        path: '/react/*',
        element: <div id="react-container" />
      },
      {
        path: '/vue/*',
        element: <div id="vue-container" />
      }
    ]
  }
])
```

### Sub-Application Routing

Sub-applications need to configure base path:

```typescript
// React sub-application
import { createBrowserRouter } from 'react-router-dom'

const router = createBrowserRouter([
  {
    path: '/react/home',
    element: <Home />
  },
  {
    path: '/react/about',
    element: <About />
  }
], {
  basename: window.__MICRO_CORE__ ? '/react' : '/'
})
```

## Application Communication

### Event Communication

```typescript
// Main application sends event
microCore.eventBus.emit('user-login', { userId: 123 })

// Sub-application listens to event
window.__MICRO_CORE__.eventBus.on('user-login', (data) => {
  console.log('User login:', data)
})
```

### State Sharing

```typescript
// Set shared state
microCore.setState('user', { name: 'John', role: 'admin' })

// Get shared state
const user = microCore.getState('user')

// Listen to state changes
microCore.onStateChange('user', (newUser, oldUser) => {
  console.log('User state changed:', newUser, oldUser)
})
```

## Development and Debugging

### Enable Debug Mode

```typescript
const microCore = new MicroCore({
  debug: true, // Enable debug mode
  devtools: true // Enable developer tools
})
```

### Using Debug Panel

Open developer tools in your browser and switch to the "Micro-Core" panel, where you can:

- View registered application list
- Monitor application state changes
- View event communication logs
- Analyze performance metrics

## Production Deployment

### Build Production Version

```bash
# Build main application
npm run build

# Build sub-applications
cd sub-app-react && npm run build
cd sub-app-vue && npm run build
```

### Deployment Configuration

```nginx
# nginx.conf
server {
    listen 80;
    server_name example.com;
    
    # Main application
    location / {
        root /var/www/main-app;
        try_files $uri $uri/ /index.html;
    }
    
    # Sub-applications
    location /react-app/ {
        root /var/www/sub-apps;
        try_files $uri $uri/ /react-app/index.html;
    }
    
    location /vue-app/ {
        root /var/www/sub-apps;
        try_files $uri $uri/ /vue-app/index.html;
    }
}
```

## Next Steps

Congratulations! You have successfully built your first Micro-Core micro-frontend application. Next, you can:

- [Learn Core Concepts](./core-concepts.md) - Deep dive into Micro-Core's design philosophy
- [View Complete Examples](../examples/) - Learn more real-world use cases
- [Explore Advanced Features](./advanced/) - Use plugin system and middleware
- [Read Best Practices](./best-practices/) - Learn production environment best practices

## FAQ

### Q: How to handle style isolation?

A: Micro-Core provides multiple style isolation solutions:

```typescript
const microCore = new MicroCore({
  sandbox: {
    css: 'scoped' // 'scoped' | 'shadow-dom' | 'none'
  }
})
```

### Q: How to handle dependency sharing between applications?

A: Use external dependencies configuration:

```typescript
const microCore = new MicroCore({
  externals: {
    'react': 'React',
    'react-dom': 'ReactDOM',
    'vue': 'Vue'
  }
})
```

### Q: How to implement application preloading?

A: Configure preloading strategy:

```typescript
microCore.registerApp({
  name: 'react-app',
  entry: 'http://localhost:3001',
  container: '#react-container',
  activeWhen: '/react',
  preload: true // Enable preloading
})
```

If you encounter other issues, please check the [Troubleshooting Guide](./troubleshooting.md) or ask questions in [GitHub Issues](https://github.com/echo008/micro-core/issues).