/**
 * @fileoverview Micro-Core 核心导出 - 精简版
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 * @description 只导出微前端运行的最基础、必需功能
 *
 * 注意：以下功能已迁移到对应的插件包中：
 * - 沙箱系统 -> @micro-core/plugin-sandbox-*
 * - 高级通信 -> @micro-core/plugin-communication
 * - 路由系统 -> @micro-core/plugin-router
 * - 开发工具 -> @micro-core/plugin-devtools
 * - 监控系统 -> @micro-core/plugin-metrics
 * - 安全系统 -> @micro-core/plugin-security
 * - 主题系统 -> @micro-core/plugin-theme
 * - 国际化 -> @micro-core/plugin-i18n
 * - 中间件 -> @micro-core/plugin-middleware
 */

// ===== 核心运行时 =====
export { AppLoader } from './runtime/app-loader';
export { AppRegistry } from './runtime/app-registry';
export { ErrorHandler } from './runtime/error-handler';
export { MicroCoreKernel } from './runtime/kernel';
export { LifecycleManager } from './runtime/lifecycle-manager';
export { PluginSystem } from './runtime/plugin-system';
export { ResourceManager } from './runtime/resource-manager';

// ===== 基础通信（事件总线） =====
export type { CommunicationMessage } from './communication';
export { EventBus } from './communication/event-bus';

// ===== 基础沙箱接口 =====
export { SandboxType } from './constants';
export { BaseSandbox } from './sandbox/base-sandbox';
export type { SandboxContext, SandboxOptions } from './sandbox/base-sandbox';

// ===== 基础路由接口 =====
export type { RouteConfig, RouteMatch } from './router';

// ===== 核心类型定义 =====
export * from './types';

// ===== 错误处理 =====
export { ERROR_CODES } from './constants';
export { MicroCoreError } from './errors';

// ===== 基础工具函数 =====
export { createLogger, formatError, generateId, isValidUrl } from './utils';

// ===== 主类 =====
import { EventBus } from './communication/event-bus';
import { MicroCoreKernel } from './runtime/kernel';

/**
 * Micro-Core 主类 - 精简版
 * 微前端框架的主入口，只包含核心必需功能
 *
 * 增强功能通过插件系统提供：
 * - 使用 @micro-core/plugin-communication 获得高级通信功能
 * - 使用 @micro-core/plugin-router 获得路由功能
 * - 使用 @micro-core/plugin-sandbox-* 获得沙箱功能
 * - 使用 @micro-core/plugin-devtools 获得开发工具
 * - 等等...
 */
export class MicroCore {
    private kernel: MicroCoreKernel;
    private eventBus: EventBus;

    constructor(options: any = {}) {
        // 初始化内核
        this.kernel = new MicroCoreKernel(options);

        // 初始化基础事件总线
        this.eventBus = new EventBus();
    }

    /**
     * 注册应用
     */
    registerApplication(config: any): void {
        this.kernel.registerApplication(config);
    }

    /**
     * 注销应用
     */
    unregisterApplication(name: string): void {
        this.kernel.unregisterApplication(name);
    }

    /**
     * 启动微前端
     */
    async start(): Promise<void> {
        await this.kernel.start();
    }

    /**
     * 停止微前端
     */
    async stop(): Promise<void> {
        await this.kernel.stop();
    }

    /**
     * 获取内核实例
     */
    getKernel(): MicroCoreKernel {
        return this.kernel;
    }

    /**
     * 获取基础事件总线
     */
    getEventBus(): EventBus {
        return this.eventBus;
    }

    /**
     * 使用插件
     */
    use(plugin: any, options?: any): void {
        this.kernel.use(plugin, options);
    }

    /**
     * 获取已安装的插件列表
     */
    getPlugins(): string[] {
        return this.kernel.getPlugins();
    }

    /**
     * 检查插件是否已安装
     */
    hasPlugin(name: string): boolean {
        return this.kernel.hasPlugin(name);
    }
}

export default MicroCore;