/**
 * @fileoverview 核心工具函数 - 仅包含核心特有的工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 * @description 通用工具函数已迁移到 @micro-core/shared/utils
 */

// Temporarily use local implementations until shared package is properly linked

/**
 * 生成微前端应用唯一ID
 * @description 核心特有的ID生成逻辑，用于微前端应用标识
 */
export function generateId(prefix = 'micro-app'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}_${timestamp}_${random}`;
}

/**
 * 验证URL是否有效
 * @description 核心特有的URL验证逻辑
 */
export function isValidUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

/**
 * 格式化错误信息
 * @description 核心特有的错误格式化逻辑
 */
export function formatError(error: any): string {
    if (error instanceof Error) {
        return `${error.name}: ${error.message}${error.stack ? '\n' + error.stack : ''}`;
    }
    if (typeof error === 'string') {
        return error;
    }
    if (typeof error === 'object' && error !== null) {
        return JSON.stringify(error, null, 2);
    }
    return String(error);
}

/**
 * 创建日志记录器 - 临时本地实现
 */
function createLogger(namespace: string) {
    const log = (level: string, message: string, ...args: any[]) => {
        const timestamp = new Date().toISOString();
        const prefix = `[${timestamp}] [${namespace}] [${level.toUpperCase()}]`;
        console.log(prefix, message, ...args);
    };

    return {
        debug: (message: string, ...args: any[]) => log('DEBUG', message, ...args),
        info: (message: string, ...args: any[]) => log('INFO', message, ...args),
        warn: (message: string, ...args: any[]) => log('WARN', message, ...args),
        error: (message: string, ...args: any[]) => log('ERROR', message, ...args),
        log: (message: string, ...args: any[]) => log('INFO', message, ...args)
    };
}

/**
 * 默认日志记录器实例
 */
export const logger = createLogger('MicroCore');

// Export local implementations for now
export { createLogger };

