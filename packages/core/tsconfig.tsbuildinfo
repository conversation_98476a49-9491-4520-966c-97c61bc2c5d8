{"fileNames": ["../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es5.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2016.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.dom.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.core.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2016.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.date.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.object.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.string.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.array.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.object.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.string.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.date.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.string.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.number.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.decorators.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./src/constants.ts", "./src/errors.ts", "./src/types/common.ts", "./src/types/app.ts", "./src/types/communication.ts", "./src/types/enums.ts", "./src/types/error.ts", "./src/types/events.ts", "./src/types/lifecycle.ts", "./src/types/plugin.ts", "./src/types/resource.ts", "./src/types/router.ts", "./src/types/sandbox.ts", "./src/types/index.ts", "./src/types.ts", "./src/utils.ts", "./src/runtime/app-loader.ts", "../../node_modules/.pnpm/eventemitter3@5.0.1/node_modules/eventemitter3/index.d.ts", "./src/communication/event-bus.ts", "./src/runtime/app-registry.ts", "./src/runtime/error-handler.ts", "./src/runtime/lifecycle-manager.ts", "./src/runtime/resource-manager.ts", "./src/runtime/kernel.ts", "./src/runtime/plugin-system.ts", "./src/communication/index.ts", "./src/sandbox/base-sandbox.ts", "./src/router/index.ts", "./src/index.ts", "../../node_modules/.pnpm/@vitest+utils@1.6.1/node_modules/@vitest/utils/dist/types.d.ts", "../../node_modules/.pnpm/@vitest+utils@1.6.1/node_modules/@vitest/utils/dist/helpers.d.ts", "../../node_modules/.pnpm/@sinclair+typebox@0.27.8/node_modules/@sinclair/typebox/typebox.d.ts", "../../node_modules/.pnpm/@jest+schemas@29.6.3/node_modules/@jest/schemas/build/index.d.ts", "../../node_modules/.pnpm/pretty-format@29.7.0/node_modules/pretty-format/build/index.d.ts", "../../node_modules/.pnpm/@vitest+utils@1.6.1/node_modules/@vitest/utils/dist/index.d.ts", "../../node_modules/.pnpm/@vitest+runner@1.6.1/node_modules/@vitest/runner/dist/tasks-k5xerdtv.d.ts", "../../node_modules/.pnpm/@vitest+utils@1.6.1/node_modules/@vitest/utils/dist/types-9l4nily8.d.ts", "../../node_modules/.pnpm/@vitest+utils@1.6.1/node_modules/@vitest/utils/dist/diff.d.ts", "../../node_modules/.pnpm/@vitest+utils@1.6.1/node_modules/@vitest/utils/diff.d.ts", "../../node_modules/.pnpm/@vitest+runner@1.6.1/node_modules/@vitest/runner/dist/types.d.ts", "../../node_modules/.pnpm/@vitest+utils@1.6.1/node_modules/@vitest/utils/dist/error.d.ts", "../../node_modules/.pnpm/@vitest+utils@1.6.1/node_modules/@vitest/utils/error.d.ts", "../../node_modules/.pnpm/@vitest+runner@1.6.1/node_modules/@vitest/runner/dist/index.d.ts", "../../node_modules/.pnpm/@vitest+runner@1.6.1/node_modules/@vitest/runner/dist/utils.d.ts", "../../node_modules/.pnpm/@vitest+runner@1.6.1/node_modules/@vitest/runner/utils.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/compatibility/disposable.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/compatibility/indexable.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/compatibility/iterators.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/compatibility/index.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/globals.typedarray.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/buffer.buffer.d.ts", "../../node_modules/.pnpm/buffer@5.7.1/node_modules/buffer/index.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/header.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/readable.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/file.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/fetch.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/formdata.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/connector.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/client.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/errors.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/dispatcher.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/global-dispatcher.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/global-origin.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/pool-stats.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/pool.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/handlers.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/balanced-pool.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-interceptor.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-client.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-pool.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-errors.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/proxy-agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/env-http-proxy-agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/retry-handler.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/retry-agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/api.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/interceptors.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/util.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/cookies.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/patch.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/websocket.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/eventsource.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/filereader.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/diagnostics-channel.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/content-type.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/cache.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/index.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/globals.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/assert.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/assert/strict.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/async_hooks.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/buffer.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/child_process.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/cluster.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/console.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/constants.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/crypto.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/dgram.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/diagnostics_channel.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/dns.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/dns/promises.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/domain.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/dom-events.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/events.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/fs.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/fs/promises.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/http.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/http2.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/https.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/inspector.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/module.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/net.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/os.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/path.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/perf_hooks.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/process.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/punycode.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/querystring.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/readline.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/readline/promises.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/repl.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/sea.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/stream.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/stream/promises.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/stream/consumers.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/stream/web.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/string_decoder.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/test.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/timers.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/timers/promises.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/tls.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/trace_events.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/tty.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/url.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/util.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/v8.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/vm.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/wasi.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/worker_threads.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/zlib.d.ts", "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node/index.d.ts", "../../node_modules/.pnpm/@types+estree@1.0.8/node_modules/@types/estree/index.d.ts", "../../node_modules/.pnpm/rollup@4.45.1/node_modules/rollup/dist/rollup.d.ts", "../../node_modules/.pnpm/vite@5.4.19_@types+node@20.19.9/node_modules/vite/types/hmrpayload.d.ts", "../../node_modules/.pnpm/vite@5.4.19_@types+node@20.19.9/node_modules/vite/types/customevent.d.ts", "../../node_modules/.pnpm/vite@5.4.19_@types+node@20.19.9/node_modules/vite/types/hot.d.ts", "../../node_modules/.pnpm/vite@5.4.19_@types+node@20.19.9/node_modules/vite/dist/node/types.d-agj9qkwt.d.ts", "../../node_modules/.pnpm/esbuild@0.21.5/node_modules/esbuild/lib/main.d.ts", "../../node_modules/.pnpm/source-map-js@1.2.1/node_modules/source-map-js/source-map.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/previous-map.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/input.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/css-syntax-error.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/declaration.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/root.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/warning.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/lazy-result.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/no-work-result.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/processor.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/result.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/document.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/rule.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/node.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/comment.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/container.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/at-rule.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/list.d.ts", "../../node_modules/.pnpm/postcss@8.5.6/node_modules/postcss/lib/postcss.d.ts", "../../node_modules/.pnpm/vite@5.4.19_@types+node@20.19.9/node_modules/vite/dist/node/runtime.d.ts", "../../node_modules/.pnpm/vite@5.4.19_@types+node@20.19.9/node_modules/vite/types/importglob.d.ts", "../../node_modules/.pnpm/vite@5.4.19_@types+node@20.19.9/node_modules/vite/types/metadata.d.ts", "../../node_modules/.pnpm/vite@5.4.19_@types+node@20.19.9/node_modules/vite/dist/node/index.d.ts", "../../node_modules/.pnpm/vite-node@1.6.1_@types+node@20.19.9/node_modules/vite-node/dist/trace-mapping.d-xyifztpm.d.ts", "../../node_modules/.pnpm/vite-node@1.6.1_@types+node@20.19.9/node_modules/vite-node/dist/index-o2irwhkf.d.ts", "../../node_modules/.pnpm/vite-node@1.6.1_@types+node@20.19.9/node_modules/vite-node/dist/index.d.ts", "../../node_modules/.pnpm/@vitest+snapshot@1.6.1/node_modules/@vitest/snapshot/dist/environment-cmigivxz.d.ts", "../../node_modules/.pnpm/@vitest+snapshot@1.6.1/node_modules/@vitest/snapshot/dist/index-s94asl6q.d.ts", "../../node_modules/.pnpm/@vitest+snapshot@1.6.1/node_modules/@vitest/snapshot/dist/index.d.ts", "../../node_modules/.pnpm/@vitest+expect@1.6.1/node_modules/@vitest/expect/dist/chai.d.cts", "../../node_modules/.pnpm/@vitest+expect@1.6.1/node_modules/@vitest/expect/dist/index.d.ts", "../../node_modules/.pnpm/@vitest+expect@1.6.1/node_modules/@vitest/expect/index.d.ts", "../../node_modules/.pnpm/tinybench@2.9.0/node_modules/tinybench/dist/index.d.cts", "../../node_modules/.pnpm/vite-node@1.6.1_@types+node@20.19.9/node_modules/vite-node/dist/client.d.ts", "../../node_modules/.pnpm/@vitest+snapshot@1.6.1/node_modules/@vitest/snapshot/dist/manager.d.ts", "../../node_modules/.pnpm/@vitest+snapshot@1.6.1/node_modules/@vitest/snapshot/manager.d.ts", "../../node_modules/.pnpm/vite-node@1.6.1_@types+node@20.19.9/node_modules/vite-node/dist/server.d.ts", "../../node_modules/.pnpm/@types+deep-eql@4.0.2/node_modules/@types/deep-eql/index.d.ts", "../../node_modules/.pnpm/@types+chai@5.2.2/node_modules/@types/chai/index.d.ts", "../../node_modules/.pnpm/vitest@1.6.1_@types+node@20.19.9_@vitest+ui@1.6.1_jsdom@23.2.0/node_modules/vitest/dist/reporters-w_64as5f.d.ts", "../../node_modules/.pnpm/vitest@1.6.1_@types+node@20.19.9_@vitest+ui@1.6.1_jsdom@23.2.0/node_modules/vitest/dist/suite-dwqifb_-.d.ts", "../../node_modules/.pnpm/@vitest+spy@1.6.1/node_modules/@vitest/spy/dist/index.d.ts", "../../node_modules/.pnpm/@vitest+snapshot@1.6.1/node_modules/@vitest/snapshot/dist/environment.d.ts", "../../node_modules/.pnpm/@vitest+snapshot@1.6.1/node_modules/@vitest/snapshot/environment.d.ts", "../../node_modules/.pnpm/vitest@1.6.1_@types+node@20.19.9_@vitest+ui@1.6.1_jsdom@23.2.0/node_modules/vitest/dist/index.d.ts", "./src/__tests__/setup.ts", "./src/runtime/index.ts", "./src/sandbox/index.ts", "./src/types/strict.ts"], "fileIdsList": [[79, 98, 141], [98, 141], [98, 141, 235], [98, 138, 141], [98, 140, 141], [141], [98, 141, 146, 175], [98, 141, 142, 147, 153, 154, 161, 172, 183], [98, 141, 142, 143, 153, 161], [93, 94, 95, 98, 141], [98, 141, 144, 184], [98, 141, 145, 146, 154, 162], [98, 141, 146, 172, 180], [98, 141, 147, 149, 153, 161], [98, 140, 141, 148], [98, 141, 149, 150], [98, 141, 151, 153], [98, 140, 141, 153], [98, 141, 153, 154, 155, 172, 183], [98, 141, 153, 154, 155, 168, 172, 175], [98, 136, 141], [98, 141, 149, 153, 156, 161, 172, 183], [98, 141, 153, 154, 156, 157, 161, 172, 180, 183], [98, 141, 156, 158, 172, 180, 183], [96, 97, 98, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189], [98, 141, 153, 159], [98, 141, 160, 183, 188], [98, 141, 149, 153, 161, 172], [98, 141, 162], [98, 141, 163], [98, 140, 141, 164], [98, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189], [98, 141, 166], [98, 141, 167], [98, 141, 153, 168, 169], [98, 141, 168, 170, 184, 186], [98, 141, 153, 172, 173, 175], [98, 141, 174, 175], [98, 141, 172, 173], [98, 141, 175], [98, 141, 176], [98, 138, 141, 172, 177], [98, 141, 153, 178, 179], [98, 141, 178, 179], [98, 141, 146, 161, 172, 180], [98, 141, 181], [98, 141, 161, 182], [98, 141, 156, 167, 183], [98, 141, 146, 184], [98, 141, 172, 185], [98, 141, 160, 186], [98, 141, 187], [98, 141, 153, 155, 164, 172, 175, 183, 186, 188], [98, 141, 172, 189], [82, 86, 98, 141], [98, 141, 228], [82, 83, 86, 87, 89, 98, 141], [82, 98, 141], [82, 83, 86, 98, 141], [82, 83, 98, 141], [91, 98, 141], [98, 141, 224], [81, 98, 141, 224], [81, 98, 141, 224, 225], [98, 141, 240], [98, 141, 232], [85, 98, 141], [81, 84, 98, 141], [77, 98, 141], [77, 78, 81, 98, 141], [81, 98, 141], [88, 98, 141], [98, 141, 213], [98, 141, 211, 213], [98, 141, 202, 210, 211, 212, 214, 216], [98, 141, 200], [98, 141, 203, 208, 213, 216], [98, 141, 199, 216], [98, 141, 203, 204, 207, 208, 209, 216], [98, 141, 203, 204, 205, 207, 208, 216], [98, 141, 200, 201, 202, 203, 204, 208, 209, 210, 212, 213, 214, 216], [98, 141, 198, 200, 201, 202, 203, 204, 205, 207, 208, 209, 210, 211, 212, 213, 214, 215], [98, 141, 198, 216], [98, 141, 203, 205, 206, 208, 209, 216], [98, 141, 207, 216], [98, 141, 208, 209, 213, 216], [98, 141, 201, 211], [80, 98, 141], [98, 141, 191, 192], [98, 108, 112, 141, 183], [98, 108, 141, 172, 183], [98, 103, 141], [98, 105, 108, 141, 180, 183], [98, 141, 161, 180], [98, 141, 190], [98, 103, 141, 190], [98, 105, 108, 141, 161, 183], [98, 100, 101, 104, 107, 141, 153, 172, 183], [98, 108, 115, 141], [98, 100, 106, 141], [98, 108, 129, 130, 141], [98, 104, 108, 141, 175, 183, 190], [98, 129, 141, 190], [98, 102, 103, 141, 190], [98, 108, 141], [98, 102, 103, 104, 105, 106, 107, 108, 109, 110, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 130, 131, 132, 133, 134, 135, 141], [98, 108, 123, 141], [98, 108, 115, 116, 141], [98, 106, 108, 116, 117, 141], [98, 107, 141], [98, 100, 103, 108, 141], [98, 108, 112, 116, 117, 141], [98, 112, 141], [98, 106, 108, 111, 141, 183], [98, 100, 105, 108, 115, 141], [98, 141, 172], [98, 103, 108, 129, 141, 188, 190], [98, 141, 221, 222], [98, 141, 221], [98, 141, 220, 221, 222, 237], [98, 141, 153, 154, 156, 157, 158, 161, 172, 180, 183, 189, 190, 192, 193, 194, 195, 196, 197, 216, 217, 218, 219], [98, 141, 193, 194, 195, 196], [98, 141, 193, 194, 195], [98, 141, 193], [98, 141, 194], [98, 141, 192], [82, 86, 90, 92, 98, 141, 154, 172, 188, 220, 223, 226, 227, 229, 230, 231, 233, 234, 237, 238, 239, 241], [82, 90, 92, 98, 141, 154, 172, 188, 220, 223, 226, 227, 229, 230, 231, 233, 234, 237], [90, 92, 98, 141, 230, 237], [98, 141, 242], [63, 65, 98, 141], [66, 98, 141], [48, 49, 62, 63, 64, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 98, 141], [62, 63, 98, 141], [48, 49, 62, 63, 66, 98, 141], [62, 64, 67, 68, 69, 70, 71, 72, 98, 141], [48, 49, 62, 63, 66, 67, 69, 70, 98, 141], [48, 49, 62, 66, 98, 141], [49, 62, 63, 98, 141], [48, 49, 62, 63, 98, 141, 153], [48, 74, 98, 141], [61, 98, 141], [48, 50, 98, 141], [48, 98, 141], [50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 98, 141]], "fileInfos": [{"version": "69684132aeb9b5642cbcd9e22dff7818ff0ee1aa831728af0ecf97d3364d5546", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "092c2bfe125ce69dbb1223c85d68d4d2397d7d8411867b5cc03cec902c233763", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "07f073f19d67f74d732b1adea08e1dc66b1b58d77cb5b43931dee3d798a2fd53", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "936e80ad36a2ee83fc3caf008e7c4c5afe45b3cf3d5c24408f039c1d47bdc1df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fef8cfad2e2dc5f5b3d97a6f4f2e92848eb1b88e897bb7318cef0e2820bceaab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d2ff70deaab8bf3767a6e336c3b0d13d44a174b4893cd5016263870e570ced8", "signature": "9880d7f5804f237fc987cee0b045c200b100b905467343916f9c449cfccdfb1d"}, {"version": "b7446e63bb69210acfc342db0939d76a59feee5a178874f8f55fe868d0a194b6", "signature": "0cb4cdc4bf7a886f341693043430f6b94a5df37a5929ef4ca8c888308cf0bbfa"}, {"version": "74283c34d134b12cbd0b9f619d4a87ba1d068598194285f9a8f1cdbfee186529", "signature": "2a2537189935d7420ae2886ba3182a7d568c7167e252ec789eab57c17341a7ec"}, "d5a70979219fa4059d5e1e8af88b5f2c15f841d2c6879a35a88a5b2c3c69a694", {"version": "d2f189256d83037f345d8cf22058b6d393a0e81a8d1d2f94d50e6cc9f9b0bbee", "signature": "35264042ebb1618d45d8a114bd7e6a79392f676636ab3098ad4bdf67fef609d9"}, "bda47c694dee1f65bb3c208fac2061b26e276dc1356290dd84aa4ed61783944a", "c7f5e1b77f5fb4dddcc5d6fe9f8d095588397c5e73625f0fee153466c39d5169", {"version": "1b11528bbd2c62d934d7b00c9b3e1fee8e5751db9e2b6b16b93eef7553ea469e", "signature": "e766a271516bda88616e24ef2bbcbfc9953c46b472537d10721d84ba54af46c4"}, {"version": "674072c9ee3acda1e5a1f66237e8a6370998e3a2c39f6bd4ea242c894ed63654", "signature": "b8160d3298c054dc2adbc215f9579a7e477d9d0851aaa523a5a1df94d59502ff"}, {"version": "7e246d0214c5089363b489b133a0724d80f15c93e7e1412297a99c3d6edf7a0c", "signature": "7b67ab2da91f83a48c264c544071dc1b7622469db2b57d8988900f9af4d5f356"}, "a7df98ff99d02a7db167020746301a0843a57fc4394e4c6a6676e36be85e0d9e", {"version": "d7e9b0fb110bc2f1f9986d5f3f9e7592938fe5f84ce7f5dbf65099bc202abbd8", "signature": "276839c96bcdc897b1dbaf339052a736067ddac81b46dd01760c09b39dae3221"}, "10a0d60fd00587046e08688060b08d82a64b6a7de36ec920543d4c31312bbc92", "df4d14a5cb0e8e077c1df9f95d20e73d7a88a0e3acb7c4daceadcca7db1f5d0e", "bedb786e785f069d6536cd22267bdeadaca7aabd993c9c31864034adffc5271b", {"version": "ed102fefbb9d855746932fe6dc2b791d01941868a2aeb4aa442e373761ee6e8c", "signature": "b337eefaf86b48d3ec896a2abc34decd40ab9fbba97c86389bfdf5451fe83a4a"}, "b037e31f63baea888f22291ecf2ebbeefc4afa75c8ee7f5d2d47939cc532b04e", {"version": "27679e96d1bd38c5938178aaf4abe8627493090b63d6bae2ce8436e6a87ebe4d", "impliedFormat": 1}, "42dd32a0459710ae1d2f26eeb1f6fc9b924684d213f3d92c2852b3385df63e39", "78f2b4de0433e4e3c8fcf4c83217eecf71804c729d207b5b81f6ad8a228dfd77", {"version": "6d37c0575659cac2ce1b563b6aee276a33a49780657b22bbfc43c295949c2dc6", "signature": "6819116c2fe19f7f9533f985204d705bafdf0608806a61d7407205bc82b1c2bb"}, "4cea6c0ac796daca5054cfbc8274c83781205903762764e4b5ffda4edff3dd10", "420e4a73e48eb99bb2130f392574264226129eabc6c7e73487982693484c6298", {"version": "f5c020e9b4c8f5eface65585c83d2cc1517c355249e0a9e02df31f604669be4c", "signature": "995990a5715b05ed46325389e0d61c5ac97c2f30fbde24f2badced3e6d78f65a"}, "31053800ea4543330ee1783f51c801e5cae09ab0b59d2c91e603c883a6e4c1d2", "a62b72fc42ae2c83979984bc98fb275947f6c33afea0b4b2ea500d54388606f5", "94c6a93408ce996530a4d4e4c014e4d666aaf8700e8f7fe8a6697837775e942d", {"version": "395fd0d5b31895ee0cce054acdf8dfc70b2e9d8af8553b245af551b68c6a7e89", "signature": "4907f38c04c614573731bd874f2e6d6cc8be72bd8052db847296420dea4418e5"}, "96439a84d927d8c1c549eaeeac23e54d99b6da9a73f7114a34385452b1b792ec", {"version": "3deed5e2a5f1e7590d44e65a5b61900158a3c38bac9048462d38b1bc8098bb2e", "impliedFormat": 99}, {"version": "d435a43f89ed8794744c59d72ce71e43c1953338303f6be9ef99086faa8591d7", "impliedFormat": 99}, {"version": "c085e9aa62d1ae1375794c1fb927a445fa105fed891a7e24edbb1c3300f7384a", "impliedFormat": 1}, {"version": "f315e1e65a1f80992f0509e84e4ae2df15ecd9ef73df975f7c98813b71e4c8da", "impliedFormat": 1}, {"version": "5b9586e9b0b6322e5bfbd2c29bd3b8e21ab9d871f82346cb71020e3d84bae73e", "impliedFormat": 1}, {"version": "a4f64e674903a21e1594a24c3fc8583f3a587336d17d41ade46aa177a8ab889b", "impliedFormat": 99}, {"version": "b6f69984ffcd00a7cbcef9c931b815e8872c792ed85d9213cb2e2c14c50ca63a", "impliedFormat": 99}, {"version": "2bbc5abe5030aa07a97aabd6d3932ed2e8b7a241cf3923f9f9bf91a0addbe41f", "impliedFormat": 99}, {"version": "1e5e5592594e16bcf9544c065656293374120eb8e78780fb6c582cc710f6db11", "impliedFormat": 99}, {"version": "05c7aef6a4e496b93c2e682cced8903c0dfe6340d04f3fe616176e2782193435", "impliedFormat": 99}, {"version": "4abf1e884eecb0bf742510d69d064e33d53ac507991d6c573958356f920c3de4", "impliedFormat": 99}, {"version": "44f1d2dd522c849ca98c4f95b8b2bc84b64408d654f75eb17ec78b8ceb84da11", "impliedFormat": 99}, {"version": "500a67e158e4025f27570ab6a99831680852bb45a44d4c3647ab7567feb1fb4c", "impliedFormat": 99}, {"version": "89edc5e1739692904fdf69edcff9e1023d2213e90372ec425b2f17e3aecbaa4a", "impliedFormat": 99}, {"version": "e7d5bcffc98eded65d620bc0b6707c307b79c21d97a5fb8601e8bdf2296026b6", "impliedFormat": 99}, {"version": "e666e31d323fef5642f87db0da48a83e58f0aaf9e3823e87eabd8ec7e0441a36", "impliedFormat": 99}, {"version": "70521b6ab0dcba37539e5303104f29b721bfb2940b2776da4cc818c07e1fefc1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ab41ef1f2cdafb8df48be20cd969d875602483859dc194e9c97c8a576892c052", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d153a11543fd884b596587ccd97aebbeed950b26933ee000f94009f1ab142848", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "21d819c173c0cf7cc3ce57c3276e77fd9a8a01d35a06ad87158781515c9a438a", "impliedFormat": 1}, {"version": "a79e62f1e20467e11a904399b8b18b18c0c6eea6b50c1168bf215356d5bebfaf", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "49a5a44f2e68241a1d2bd9ec894535797998841c09729e506a7cbfcaa40f2180", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e9c23ba78aabc2e0a27033f18737a6df754067731e69dc5f52823957d60a4b6", "impliedFormat": 1}, {"version": "5929864ce17fba74232584d90cb721a89b7ad277220627cc97054ba15a98ea8f", "impliedFormat": 1}, {"version": "763fe0f42b3d79b440a9b6e51e9ba3f3f91352469c1e4b3b67bfa4ff6352f3f4", "impliedFormat": 1}, {"version": "25c8056edf4314820382a5fdb4bb7816999acdcb929c8f75e3f39473b87e85bc", "impliedFormat": 1}, {"version": "c464d66b20788266e5353b48dc4aa6bc0dc4a707276df1e7152ab0c9ae21fad8", "impliedFormat": 1}, {"version": "78d0d27c130d35c60b5e5566c9f1e5be77caf39804636bc1a40133919a949f21", "impliedFormat": 1}, {"version": "c6fd2c5a395f2432786c9cb8deb870b9b0e8ff7e22c029954fabdd692bff6195", "impliedFormat": 1}, {"version": "1d6e127068ea8e104a912e42fc0a110e2aa5a66a356a917a163e8cf9a65e4a75", "impliedFormat": 1}, {"version": "5ded6427296cdf3b9542de4471d2aa8d3983671d4cac0f4bf9c637208d1ced43", "impliedFormat": 1}, {"version": "7f182617db458e98fc18dfb272d40aa2fff3a353c44a89b2c0ccb3937709bfb5", "impliedFormat": 1}, {"version": "cadc8aced301244057c4e7e73fbcae534b0f5b12a37b150d80e5a45aa4bebcbd", "impliedFormat": 1}, {"version": "385aab901643aa54e1c36f5ef3107913b10d1b5bb8cbcd933d4263b80a0d7f20", "impliedFormat": 1}, {"version": "9670d44354bab9d9982eca21945686b5c24a3f893db73c0dae0fd74217a4c219", "impliedFormat": 1}, {"version": "0b8a9268adaf4da35e7fa830c8981cfa22adbbe5b3f6f5ab91f6658899e657a7", "impliedFormat": 1}, {"version": "11396ed8a44c02ab9798b7dca436009f866e8dae3c9c25e8c1fbc396880bf1bb", "impliedFormat": 1}, {"version": "ba7bc87d01492633cb5a0e5da8a4a42a1c86270e7b3d2dea5d156828a84e4882", "impliedFormat": 1}, {"version": "4893a895ea92c85345017a04ed427cbd6a1710453338df26881a6019432febdd", "impliedFormat": 1}, {"version": "c21dc52e277bcfc75fac0436ccb75c204f9e1b3fa5e12729670910639f27343e", "impliedFormat": 1}, {"version": "13f6f39e12b1518c6650bbb220c8985999020fe0f21d818e28f512b7771d00f9", "impliedFormat": 1}, {"version": "9b5369969f6e7175740bf51223112ff209f94ba43ecd3bb09eefff9fd675624a", "impliedFormat": 1}, {"version": "4fe9e626e7164748e8769bbf74b538e09607f07ed17c2f20af8d680ee49fc1da", "impliedFormat": 1}, {"version": "24515859bc0b836719105bb6cc3d68255042a9f02a6022b3187948b204946bd2", "impliedFormat": 1}, {"version": "ea0148f897b45a76544ae179784c95af1bd6721b8610af9ffa467a518a086a43", "impliedFormat": 1}, {"version": "24c6a117721e606c9984335f71711877293a9651e44f59f3d21c1ea0856f9cc9", "impliedFormat": 1}, {"version": "dd3273ead9fbde62a72949c97dbec2247ea08e0c6952e701a483d74ef92d6a17", "impliedFormat": 1}, {"version": "405822be75ad3e4d162e07439bac80c6bcc6dbae1929e179cf467ec0b9ee4e2e", "impliedFormat": 1}, {"version": "0db18c6e78ea846316c012478888f33c11ffadab9efd1cc8bcc12daded7a60b6", "impliedFormat": 1}, {"version": "e61be3f894b41b7baa1fbd6a66893f2579bfad01d208b4ff61daef21493ef0a8", "impliedFormat": 1}, {"version": "bd0532fd6556073727d28da0edfd1736417a3f9f394877b6d5ef6ad88fba1d1a", "impliedFormat": 1}, {"version": "89167d696a849fce5ca508032aabfe901c0868f833a8625d5a9c6e861ef935d2", "impliedFormat": 1}, {"version": "615ba88d0128ed16bf83ef8ccbb6aff05c3ee2db1cc0f89ab50a4939bfc1943f", "impliedFormat": 1}, {"version": "a4d551dbf8746780194d550c88f26cf937caf8d56f102969a110cfaed4b06656", "impliedFormat": 1}, {"version": "8bd86b8e8f6a6aa6c49b71e14c4ffe1211a0e97c80f08d2c8cc98838006e4b88", "impliedFormat": 1}, {"version": "317e63deeb21ac07f3992f5b50cdca8338f10acd4fbb7257ebf56735bf52ab00", "impliedFormat": 1}, {"version": "4732aec92b20fb28c5fe9ad99521fb59974289ed1e45aecb282616202184064f", "impliedFormat": 1}, {"version": "2e85db9e6fd73cfa3d7f28e0ab6b55417ea18931423bd47b409a96e4a169e8e6", "impliedFormat": 1}, {"version": "c46e079fe54c76f95c67fb89081b3e399da2c7d109e7dca8e4b58d83e332e605", "impliedFormat": 1}, {"version": "bf67d53d168abc1298888693338cb82854bdb2e69ef83f8a0092093c2d562107", "impliedFormat": 1}, {"version": "1ca84b44ad1d8e4576f24904d8b95dd23b94ea67e1575f89614ac90062fc67f4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "6d586db0a09a9495ebb5dece28f54df9684bfbd6e1f568426ca153126dac4a40", "impliedFormat": 1}, {"version": "7394959e5a741b185456e1ef5d64599c36c60a323207450991e7a42e08911419", "impliedFormat": 1}, {"version": "8c0bcd6c6b67b4b503c11e91a1fb91522ed585900eab2ab1f61bba7d7caa9d6f", "impliedFormat": 1}, {"version": "567b7f607f400873151d7bc63a049514b53c3c00f5f56e9e95695d93b66a138e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3e58c4c18a031cbb17abec7a4ad0bd5ae9fc70c1f4ba1e7fb921ad87c504aca", "impliedFormat": 1}, {"version": "84c1930e33d1bb12ad01bcbe11d656f9646bd21b2fb2afd96e8e10615a021aef", "impliedFormat": 1}, {"version": "35ec8b6760fd7138bbf5809b84551e31028fb2ba7b6dc91d95d098bf212ca8b4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5524481e56c48ff486f42926778c0a3cce1cc85dc46683b92b1271865bcf015a", "impliedFormat": 1}, {"version": "4b87f767c7bc841511113c876a6b8bf1fd0cb0b718c888ad84478b372ec486b1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d04e3640dd9eb67f7f1e5bd3d0bf96c784666f7aefc8ac1537af6f2d38d4c29", "impliedFormat": 1}, {"version": "9d19808c8c291a9010a6c788e8532a2da70f811adb431c97520803e0ec649991", "impliedFormat": 1}, {"version": "2bf469abae4cc9c0f340d4e05d9d26e37f936f9c8ca8f007a6534f109dcc77e4", "impliedFormat": 1}, {"version": "4aacb0dd020eeaef65426153686cc639a78ec2885dc72ad220be1d25f1a439df", "impliedFormat": 1}, {"version": "f0bd7e6d931657b59605c44112eaf8b980ba7f957a5051ed21cb93d978cf2f45", "impliedFormat": 1}, {"version": "71450bbc2d82821d24ca05699a533e72758964e9852062c53b30f31c36978ab8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0ada07543808f3b967624645a8e1ccd446f8b01ade47842acf1328aec899fed0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4c21aaa8257d7950a5b75a251d9075b6a371208fc948c9c8402f6690ef3b5b55", "impliedFormat": 1}, {"version": "b5895e6353a5d708f55d8685c38a235c3a6d8138e374dee8ceb8ffde5aa8002a", "impliedFormat": 1}, {"version": "54c4f21f578864961efc94e8f42bc893a53509e886370ec7dd602e0151b9266c", "impliedFormat": 1}, {"version": "de735eca2c51dd8b860254e9fdb6d9ec19fe402dfe597c23090841ce3937cfc5", "impliedFormat": 1}, {"version": "4ff41188773cbf465807dd2f7059c7494cbee5115608efc297383832a1150c43", "impliedFormat": 1}, {"version": "5650cf3dace09e7c25d384e3e6b818b938f68f4e8de96f52d9c5a1b3db068e86", "impliedFormat": 1}, {"version": "1354ca5c38bd3fd3836a68e0f7c9f91f172582ba30ab15bb8c075891b91502b7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5155da3047ef977944d791a2188ff6e6c225f6975cc1910ab7bb6838ab84cede", "impliedFormat": 1}, {"version": "93f437e1398a4f06a984f441f7fa7a9f0535c04399619b5c22e0b87bdee182cb", "impliedFormat": 1}, {"version": "afbe24ab0d74694372baa632ecb28bb375be53f3be53f9b07ecd7fc994907de5", "impliedFormat": 1}, {"version": "e16d218a30f6a6810b57f7e968124eaa08c7bb366133ea34bbf01e7cd6b8c0ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb8692dea24c27821f77e397272d9ed2eda0b95e4a75beb0fdda31081d15a8ae", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e043a1bc8fbf2a255bccf9bf27e0f1caf916c3b0518ea34aa72357c0afd42ec", "impliedFormat": 1}, {"version": "b4f70ec656a11d570e1a9edce07d118cd58d9760239e2ece99306ee9dfe61d02", "impliedFormat": 1}, {"version": "3bc2f1e2c95c04048212c569ed38e338873f6a8593930cf5a7ef24ffb38fc3b6", "impliedFormat": 1}, {"version": "8145e07aad6da5f23f2fcd8c8e4c5c13fb26ee986a79d03b0829b8fce152d8b2", "impliedFormat": 1}, {"version": "f9d9d753d430ed050dc1bf2667a1bab711ccbb1c1507183d794cc195a5b085cc", "impliedFormat": 1}, {"version": "9eece5e586312581ccd106d4853e861aaaa1a39f8e3ea672b8c3847eedd12f6e", "impliedFormat": 1}, {"version": "5b6844ad931dcc1d3aca53268f4bd671428421464b1286746027aede398094f2", "impliedFormat": 1}, {"version": "37ba7b45141a45ce6e80e66f2a96c8a5ab1bcef0fc2d0f56bb58df96ec67e972", "impliedFormat": 1}, {"version": "125d792ec6c0c0f657d758055c494301cc5fdb327d9d9d5960b3f129aff76093", "impliedFormat": 1}, {"version": "0dbcebe2126d03936c70545e96a6e41007cf065be38a1ce4d32a39fcedefead4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1851a3b4db78664f83901bb9cac9e45e03a37bb5933cc5bf37e10bb7e91ab4eb", "impliedFormat": 1}, {"version": "461e54289e6287e8494a0178ba18182acce51a02bca8dea219149bf2cf96f105", "impliedFormat": 1}, {"version": "12ed4559eba17cd977aa0db658d25c4047067444b51acfdcbf38470630642b23", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3ffabc95802521e1e4bcba4c88d8615176dc6e09111d920c7a213bdda6e1d65", "impliedFormat": 1}, {"version": "e31e51c55800014d926e3f74208af49cb7352803619855c89296074d1ecbb524", "impliedFormat": 1}, {"version": "ae56f65caf3be91108707bd8dfbccc2a57a91feb5daabf7165a06a945545ed26", "impliedFormat": 1}, {"version": "a136d5de521da20f31631a0a96bf712370779d1c05b7015d7019a9b2a0446ca9", "impliedFormat": 1}, {"version": "dfb96ba5177b68003deec9e773c47257da5c4c8a74053d8956389d832df72002", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "92d3070580cf72b4bb80959b7f16ede9a3f39e6f4ef2ac87cfa4561844fdc69f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d3dffd70e6375b872f0b4e152de4ae682d762c61a24881ecc5eb9f04c5caf76f", "impliedFormat": 1}, {"version": "613deebaec53731ff6b74fe1a89f094b708033db6396b601df3e6d5ab0ec0a47", "impliedFormat": 1}, {"version": "d91a7d8b5655c42986f1bdfe2105c4408f472831c8f20cf11a8c3345b6b56c8c", "impliedFormat": 1}, {"version": "e56eb632f0281c9f8210eb8c86cc4839a427a4ffffcfd2a5e40b956050b3e042", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e8a979b8af001c9fc2e774e7809d233c8ca955a28756f52ee5dee88ccb0611d2", "impliedFormat": 1}, {"version": "cac793cc47c29e26e4ac3601dcb00b4435ebed26203485790e44f2ad8b6ad847", "impliedFormat": 1}, {"version": "151ff381ef9ff8da2da9b9663ebf657eac35c4c9a19183420c05728f31a6761d", "impliedFormat": 1}, {"version": "ffb518fc55181aefd066c690dbc0f8fa6a1533c8ddac595469c8c5f7fda2d756", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "282f98006ed7fa9bb2cd9bdbe2524595cfc4bcd58a0bb3232e4519f2138df811", "impliedFormat": 1}, {"version": "6222e987b58abfe92597e1273ad7233626285bc2d78409d4a7b113d81a83496b", "impliedFormat": 1}, {"version": "cbe726263ae9a7bf32352380f7e8ab66ee25b3457137e316929269c19e18a2be", "impliedFormat": 1}, {"version": "8b96046bf5fb0a815cba6b0880d9f97b7f3a93cf187e8dcfe8e2792e97f38f87", "impliedFormat": 99}, {"version": "bacf2c84cf448b2cd02c717ad46c3d7fd530e0c91282888c923ad64810a4d511", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "402e5c534fb2b85fa771170595db3ac0dd532112c8fa44fc23f233bc6967488b", "impliedFormat": 1}, {"version": "8885cf05f3e2abf117590bbb951dcf6359e3e5ac462af1c901cfd24c6a6472e2", "impliedFormat": 1}, {"version": "333caa2bfff7f06017f114de738050dd99a765c7eb16571c6d25a38c0d5365dc", "impliedFormat": 1}, {"version": "e61df3640a38d535fd4bc9f4a53aef17c296b58dc4b6394fd576b808dd2fe5e6", "impliedFormat": 1}, {"version": "459920181700cec8cbdf2a5faca127f3f17fd8dd9d9e577ed3f5f3af5d12a2e4", "impliedFormat": 1}, {"version": "4719c209b9c00b579553859407a7e5dcfaa1c472994bd62aa5dd3cc0757eb077", "impliedFormat": 1}, {"version": "7ec359bbc29b69d4063fe7dad0baaf35f1856f914db16b3f4f6e3e1bca4099fa", "impliedFormat": 1}, {"version": "70790a7f0040993ca66ab8a07a059a0f8256e7bb57d968ae945f696cbff4ac7a", "impliedFormat": 1}, {"version": "d1b9a81e99a0050ca7f2d98d7eedc6cda768f0eb9fa90b602e7107433e64c04c", "impliedFormat": 1}, {"version": "a022503e75d6953d0e82c2c564508a5c7f8556fad5d7f971372d2d40479e4034", "impliedFormat": 1}, {"version": "b215c4f0096f108020f666ffcc1f072c81e9f2f95464e894a5d5f34c5ea2a8b1", "impliedFormat": 1}, {"version": "644491cde678bd462bb922c1d0cfab8f17d626b195ccb7f008612dc31f445d2d", "impliedFormat": 1}, {"version": "dfe54dab1fa4961a6bcfba68c4ca955f8b5bbeb5f2ab3c915aa7adaa2eabc03a", "impliedFormat": 1}, {"version": "1251d53755b03cde02466064260bb88fd83c30006a46395b7d9167340bc59b73", "impliedFormat": 1}, {"version": "47865c5e695a382a916b1eedda1b6523145426e48a2eae4647e96b3b5e52024f", "impliedFormat": 1}, {"version": "4cdf27e29feae6c7826cdd5c91751cc35559125e8304f9e7aed8faef97dcf572", "impliedFormat": 1}, {"version": "331b8f71bfae1df25d564f5ea9ee65a0d847c4a94baa45925b6f38c55c7039bf", "impliedFormat": 1}, {"version": "2a771d907aebf9391ac1f50e4ad37952943515eeea0dcc7e78aa08f508294668", "impliedFormat": 1}, {"version": "0146fd6262c3fd3da51cb0254bb6b9a4e42931eb2f56329edd4c199cb9aaf804", "impliedFormat": 1}, {"version": "82e687ebd99518bc63ea04b0c3810fb6e50aa6942decd0ca6f7a56d9b9a212a6", "impliedFormat": 99}, {"version": "7f698624bbbb060ece7c0e51b7236520ebada74b747d7523c7df376453ed6fea", "impliedFormat": 1}, {"version": "8f07f2b6514744ac96e51d7cb8518c0f4de319471237ea10cf688b8d0e9d0225", "impliedFormat": 1}, {"version": "257b83faa134d971c738a6b9e4c47e59bb7b23274719d92197580dd662bfafc3", "impliedFormat": 99}, {"version": "4a27c79c57a6692abb196711f82b8b07a27908c94652148d5469887836390116", "impliedFormat": 99}, {"version": "f42400484f181c2c2d7557c0ed3b8baaace644a9e943511f3d35ac6be6eb5257", "impliedFormat": 99}, {"version": "54b381d36b35df872159a8d3b52e8d852659ee805695a867a388c8ccbf57521b", "impliedFormat": 99}, {"version": "c67b4c864ec9dcde25f7ad51b90ae9fe1f6af214dbd063d15db81194fe652223", "impliedFormat": 99}, {"version": "7a4aa00aaf2160278aeae3cf0d2fc6820cf22b86374efa7a00780fbb965923ff", "impliedFormat": 99}, {"version": "66e3ee0a655ff3698be0aef05f7b76ac34c349873e073cde46d43db795b79f04", "impliedFormat": 99}, {"version": "48c411efce1848d1ed55de41d7deb93cbf7c04080912fd87aa517ed25ef42639", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "28e065b6fb60a04a538b5fbf8c003d7dac3ae9a49eddc357c2a14f2ffe9b3185", "affectsGlobalScope": true, "impliedFormat": 99}, {"version": "fe2d63fcfdde197391b6b70daf7be8c02a60afa90754a5f4a04bdc367f62793d", "impliedFormat": 99}, {"version": "69bf2422313487956e4dacf049f30cb91b34968912058d244cb19e4baa24da97", "impliedFormat": 1}, {"version": "0d87708dafcde5468a130dfe64fac05ecad8328c298a4f0f2bd86603e5fd002e", "impliedFormat": 99}, {"version": "a3f2554ba6726d0da0ffdc15b675b8b3de4aea543deebbbead845680b740a7fd", "impliedFormat": 99}, {"version": "b7e28e06011460436d5c2ec2996846ac0c451e135357fc5a7269e5665a32fbd7", "impliedFormat": 99}, {"version": "93dda0982b139b27b85dd2924d23e07ee8b4ca36a10be7bdf361163e4ffcc033", "impliedFormat": 99}, {"version": "427fe2004642504828c1476d0af4270e6ad4db6de78c0b5da3e4c5ca95052a99", "impliedFormat": 1}, {"version": "c8905dbea83f3220676a669366cd8c1acef56af4d9d72a8b2241b1d044bb4302", "affectsGlobalScope": true, "impliedFormat": 99}, {"version": "d7b652822e2a387fd2bcf0b78bcf2b7a9a9e73c4a71c12c5d0bbbb367aea6a87", "affectsGlobalScope": true, "impliedFormat": 99}, {"version": "cb80558784fc93165b64809b3ba66266d10585d838709ebf5e4576f63f9f2929", "impliedFormat": 99}, {"version": "dfa6bb848807bc5e01e84214d4ec13ee8ffe5e1142546dcbb32065783a5db468", "impliedFormat": 99}, {"version": "2f1ffc29f9ba7b005c0c48e6389536a245837264c99041669e0b768cfab6711d", "impliedFormat": 99}, {"version": "f2d1a59a658165341b0e2b7879aa2e19ea6a709146b2d3f70ee8a07159d3d08e", "impliedFormat": 99}, {"version": "b4270f889835e50044bf80e479fef2482edd69daf4b168f9e3ee34cf817ae41a", "impliedFormat": 99}, {"version": "20b0cbdcacf9dcf3803ef7df1c6cab61a8a60e3c1da3624e44e74b470b35be25", "signature": "cbb1a4ca826ca948f126f088b91298c3265892c75efd6c24cd56de536847faa7"}, "27c43e59b4ad1dcb3c6df8a94ed9d30aab34f3c66db0e25620d501e0a651fe93", "c16f9425b0d3428622a6d9fd6e11081c184cd98bcf9dbb9da7e11a294c6ce7de", {"version": "51f8b25a3b89b0f6f8ec1b3b2ac76b133d80fbdd00148dd0b1e84ebc940f6a64", "signature": "9a7fcfce463de002ee41e64690952a91de58452f28e124a7ebc344ec33d13f40"}], "root": [[48, 64], [66, 76], [243, 246]], "options": {"allowJs": true, "allowSyntheticDefaultImports": true, "checkJs": false, "composite": true, "declaration": true, "declarationMap": true, "emitDecoratorMetadata": true, "esModuleInterop": true, "exactOptionalPropertyTypes": true, "experimentalDecorators": true, "importHelpers": true, "jsx": 4, "jsxImportSource": "react", "module": 99, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitOverride": true, "noImplicitReturns": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "./dist", "removeComments": false, "rootDir": "./src", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": 7}, "referencedMap": [[80, 1], [79, 2], [236, 3], [235, 2], [191, 2], [138, 4], [139, 4], [140, 5], [98, 6], [141, 7], [142, 8], [143, 9], [93, 2], [96, 10], [94, 2], [95, 2], [144, 11], [145, 12], [146, 13], [147, 14], [148, 15], [149, 16], [150, 16], [152, 2], [151, 17], [153, 18], [154, 19], [155, 20], [137, 21], [97, 2], [156, 22], [157, 23], [158, 24], [190, 25], [159, 26], [160, 27], [161, 28], [162, 29], [163, 30], [164, 31], [165, 32], [166, 33], [167, 34], [168, 35], [169, 35], [170, 36], [171, 2], [172, 37], [174, 38], [173, 39], [175, 40], [176, 41], [177, 42], [178, 43], [179, 44], [180, 45], [181, 46], [182, 47], [183, 48], [184, 49], [185, 50], [186, 51], [187, 52], [188, 53], [189, 54], [227, 2], [228, 55], [229, 56], [90, 57], [83, 58], [87, 59], [91, 60], [92, 61], [224, 2], [240, 62], [225, 63], [226, 64], [232, 64], [241, 65], [233, 66], [239, 2], [86, 67], [85, 68], [88, 68], [78, 69], [82, 70], [84, 71], [77, 2], [89, 72], [99, 2], [197, 2], [65, 2], [214, 73], [212, 74], [213, 75], [201, 76], [202, 74], [209, 77], [200, 78], [205, 79], [215, 2], [206, 80], [211, 81], [216, 82], [199, 83], [207, 84], [208, 85], [203, 86], [210, 73], [204, 87], [81, 88], [192, 89], [198, 2], [230, 2], [46, 2], [47, 2], [8, 2], [9, 2], [11, 2], [10, 2], [2, 2], [12, 2], [13, 2], [14, 2], [15, 2], [16, 2], [17, 2], [18, 2], [19, 2], [3, 2], [20, 2], [21, 2], [4, 2], [22, 2], [26, 2], [23, 2], [24, 2], [25, 2], [27, 2], [28, 2], [29, 2], [5, 2], [30, 2], [31, 2], [32, 2], [33, 2], [6, 2], [37, 2], [34, 2], [35, 2], [36, 2], [38, 2], [7, 2], [39, 2], [44, 2], [45, 2], [40, 2], [41, 2], [42, 2], [43, 2], [1, 2], [115, 90], [125, 91], [114, 90], [135, 92], [106, 93], [105, 94], [134, 95], [128, 96], [133, 97], [108, 98], [122, 99], [107, 100], [131, 101], [103, 102], [102, 95], [132, 103], [104, 104], [109, 105], [110, 2], [113, 105], [100, 2], [136, 106], [126, 107], [117, 108], [118, 109], [120, 110], [116, 111], [119, 112], [129, 95], [111, 113], [112, 114], [121, 115], [101, 116], [124, 107], [123, 105], [127, 2], [130, 117], [231, 118], [222, 119], [223, 118], [234, 120], [221, 2], [220, 121], [217, 122], [196, 123], [194, 124], [193, 2], [195, 125], [218, 2], [219, 126], [242, 127], [237, 128], [238, 129], [243, 130], [66, 131], [73, 132], [48, 2], [49, 2], [76, 133], [75, 2], [64, 134], [67, 135], [68, 2], [244, 136], [71, 137], [69, 138], [72, 139], [70, 140], [74, 139], [245, 141], [62, 142], [51, 143], [50, 144], [52, 2], [53, 144], [54, 144], [55, 2], [61, 145], [56, 2], [57, 2], [58, 144], [59, 2], [60, 144], [246, 2], [63, 2]], "semanticDiagnosticsPerFile": [[51, [{"start": 4261, "length": 9, "code": 2430, "category": 1, "messageText": {"messageText": "Interface 'LoadedApp' incorrectly extends interface 'AppInfo'.", "category": 1, "code": 2430, "next": [{"messageText": "Types of property 'sandbox' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'SandboxInstanceType' has no properties in common with type 'SandboxConfig'.", "category": 1, "code": 2559}]}]}}]], [57, [{"start": 583, "length": 9, "messageText": "Cannot find name 'AppConfig'.", "category": 1, "code": 2304}, {"start": 597, "length": 9, "messageText": "Cannot find name 'AppConfig'.", "category": 1, "code": 2304}, {"start": 617, "length": 9, "messageText": "Cannot find name 'AppConfig'.", "category": 1, "code": 2304}, {"start": 654, "length": 11, "messageText": "Cannot find name 'App<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 720, "length": 11, "messageText": "Cannot find name 'App<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 861, "length": 11, "messageText": "Cannot find name 'App<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 925, "length": 11, "messageText": "Cannot find name 'App<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 986, "length": 11, "messageText": "Cannot find name 'App<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 1046, "length": 11, "messageText": "Cannot find name 'App<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 1109, "length": 11, "messageText": "Cannot find name 'App<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 1171, "length": 11, "messageText": "Cannot find name 'App<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 1233, "length": 11, "messageText": "Cannot find name 'App<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 1327, "length": 11, "messageText": "Cannot find name 'App<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}]], [61, [{"start": 111, "length": 25, "messageText": "Module './app' has already exported a member named 'AppEntry'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}, {"start": 137, "length": 32, "messageText": "Module './common' has already exported a member named 'EventListener'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}, {"start": 220, "length": 25, "messageText": "Module './communication' has already exported a member named 'EventMap'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}, {"start": 275, "length": 25, "messageText": "Module './common' has already exported a member named 'Plugin'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}, {"start": 301, "length": 27, "messageText": "Module './app' has already exported a member named 'ResourceInfo'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}, {"start": 355, "length": 26, "messageText": "Module './common' has already exported a member named 'SandboxInstance'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}]], [62, [{"start": 231, "length": 9, "messageText": "Module '\"./types/index\"' has no exported member 'AppStatus'.", "category": 1, "code": 2305}, {"start": 246, "length": 10, "messageText": "Module '\"./types/index\"' has no exported member 'ErrorCodes'.", "category": 1, "code": 2305}, {"start": 415, "length": 11, "messageText": "Module '\"./types/index\"' has no exported member 'SandboxType'.", "category": 1, "code": 2305}]], [64, [{"start": 1886, "length": 5, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'AppEntry' is not assignable to parameter of type 'Record<string, string>'.", "category": 1, "code": 2345, "next": [{"messageText": "Index signature for type 'string' is missing in type 'AppEntry'.", "category": 1, "code": 2329}]}}, {"start": 2119, "length": 6, "code": 2322, "category": 1, "messageText": "Type '\"loaded\"' is not assignable to type 'AppStatusType'.", "relatedInformation": [{"file": "./src/types/app.ts", "start": 1960, "length": 6, "messageText": "The expected type comes from property 'status' which is declared here on type 'LoadedApp'", "category": 3, "code": 6500}]}, {"start": 2360, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 2737, "length": 7, "messageText": "'options' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2839, "length": 7, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'content' does not exist in type 'ResourceInfo'."}, {"start": 2980, "length": 7, "messageText": "'options' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 3257, "length": 7, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'content' does not exist in type 'ResourceInfo'."}, {"start": 3438, "length": 7, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'content' does not exist in type 'ResourceInfo'."}, {"start": 4009, "length": 7, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'content' does not exist in type 'ResourceInfo'."}, {"start": 4160, "length": 3, "code": 2322, "category": 1, "messageText": "Type 'null' is not assignable to type 'string'.", "relatedInformation": [{"file": "./src/types/app.ts", "start": 3648, "length": 3, "messageText": "The expected type comes from property 'url' which is declared here on type 'ResourceInfo'", "category": 3, "code": 6500}]}, {"start": 4539, "length": 7, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'content' does not exist in type 'ResourceInfo'."}, {"start": 4778, "length": 3, "code": 2322, "category": 1, "messageText": "Type 'null' is not assignable to type 'string'.", "relatedInformation": [{"file": "./src/types/app.ts", "start": 3648, "length": 3, "messageText": "The expected type comes from property 'url' which is declared here on type 'ResourceInfo'", "category": 3, "code": 6500}]}, {"start": 5254, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'inline' does not exist on type 'ResourceInfo'."}, {"start": 5299, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'content' does not exist on type 'ResourceInfo'."}, {"start": 5808, "length": 6, "messageText": "'config' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [67, [{"start": 1078, "length": 11, "code": 2375, "category": 1, "messageText": {"messageText": "Type '{ name: string; entry: string | AppEntry; container: string | HTMLElement; activeWhen: ActiveWhen | undefined; props: AppProps; sandbox: SandboxConfig | undefined; ... 10 more ...; updatedAt: number; }' is not assignable to type 'AppInstance' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.", "category": 1, "code": 2375, "next": [{"messageText": "Types of property 'activeWhen' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'ActiveWhen | undefined' is not assignable to type 'ActiveWhen'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'ActiveWhen'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2375, "messageText": "Type '{ name: string; entry: string | AppEntry; container: string | HTMLElement; activeWhen: ActiveWhen | undefined; props: AppProps; sandbox: SandboxConfig | undefined; ... 10 more ...; updatedAt: number; }' is not assignable to type 'AppInstance' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties."}}]}]}}, {"start": 2348, "length": 25, "code": 2551, "category": 1, "messageText": "Property 'APPLICATION_STILL_MOUNTED' does not exist on type '{ readonly UNKNOWN_ERROR: \"UNKNOWN_ERROR\"; readonly INVALID_ARGUMENT: \"INVALID_ARGUMENT\"; readonly OPERATION_FAILED: \"OPERATION_FAILED\"; readonly APPLICATION_ERROR: \"APPLICATION_ERROR\"; ... 29 more ...; readonly LIFECYCLE_TIMEOUT: \"LIFECYCLE_TIMEOUT\"; }'. Did you mean 'APPLICATION_NOT_FOUND'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 1245, "length": 46, "messageText": "'APPLICATION_NOT_FOUND' is declared here.", "category": 3, "code": 2728}]}, {"start": 3287, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'config' does not exist on type 'AppInstance'."}, {"start": 4186, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'LOADED' does not exist on type '{ readonly NOT_LOADED: \"NOT_LOADED\"; readonly LOADING_SOURCE_CODE: \"LOADING_SOURCE_CODE\"; readonly NOT_BOOTSTRAPPED: \"NOT_BOOTSTRAPPED\"; readonly BOOTSTRAPPING: \"BOOTSTRAPPING\"; ... 7 more ...; readonly SKIP_BECAUSE_BROKEN: \"SKIP_BECAUSE_BROKEN\"; }'."}, {"start": 4378, "length": 9, "code": 2551, "category": 1, "messageText": "Property 'UNMOUNTED' does not exist on type '{ readonly NOT_LOADED: \"NOT_LOADED\"; readonly LOADING_SOURCE_CODE: \"LOADING_SOURCE_CODE\"; readonly NOT_BOOTSTRAPPED: \"NOT_BOOTSTRAPPED\"; readonly BOOTSTRAPPING: \"BOOTSTRAPPING\"; ... 7 more ...; readonly SKIP_BECAUSE_BROKEN: \"SKIP_BECAUSE_BROKEN\"; }'. Did you mean 'MOUNTED'?", "relatedInformation": [{"file": "./src/constants.ts", "start": 579, "length": 18, "messageText": "'MOUNTED' is declared here.", "category": 3, "code": 2728}]}, {"start": 4409, "length": 11, "code": 2551, "category": 1, "messageText": "Property 'unmountTime' does not exist on type 'AppInstance'. Did you mean 'mountTime'?", "relatedInformation": [{"file": "./src/types/app.ts", "start": 2041, "length": 9, "messageText": "'mountTime' is declared here.", "category": 3, "code": 2728}]}, {"start": 5670, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'LOADED' does not exist on type '{ readonly NOT_LOADED: \"NOT_LOADED\"; readonly LOADING_SOURCE_CODE: \"LOADING_SOURCE_CODE\"; readonly NOT_BOOTSTRAPPED: \"NOT_BOOTSTRAPPED\"; readonly BOOTSTRAPPING: \"BOOTSTRAPPING\"; ... 7 more ...; readonly SKIP_BECAUSE_BROKEN: \"SKIP_BECAUSE_BROKEN\"; }'."}]], [68, [{"start": 100, "length": 10, "messageText": "Cannot find module './errors' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 834, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'code' does not exist on type 'ExtendedMicroCoreError'."}, {"start": 849, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'message' does not exist on type 'ExtendedMicroCoreError'."}, {"start": 1515, "length": 18, "code": 2412, "category": 1, "messageText": {"messageText": "Type '((error: ExtendedMicroCoreError) => void) | undefined' is not assignable to type '(error: ExtendedMicroCoreError) => void' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.", "category": 1, "code": 2412, "next": [{"messageText": "Type 'undefined' is not assignable to type '(error: ExtendedMicroCoreError) => void'.", "category": 1, "code": 2322}]}}, {"start": 2159, "length": 8, "messageText": "Property 'NODE_ENV' comes from an index signature, so it must be accessed with ['NODE_ENV'].", "category": 1, "code": 4111}, {"start": 3772, "length": 23, "messageText": "Object is possibly 'undefined'.", "category": 1, "code": 2532}, {"start": 3904, "length": 25, "messageText": "Object is possibly 'undefined'.", "category": 1, "code": 2532}, {"start": 5982, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'message' does not exist on type 'ExtendedMicroCoreError'."}, {"start": 6051, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'code' does not exist on type 'ExtendedMicroCoreError'."}, {"start": 6145, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'stack' does not exist on type 'ExtendedMicroCoreError'."}, {"start": 6179, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'context' does not exist on type 'ExtendedMicroCoreError'."}, {"start": 7726, "length": 10, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | undefined' is not assignable to parameter of type 'ERROR_TYPES'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'undefined' is not assignable to type 'ERROR_TYPES'.", "category": 1, "code": 2322}]}}]], [69, [{"start": 132, "length": 26, "messageText": "Cannot find module '@micro-core/shared/utils' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 3076, "length": 17, "messageText": "Duplicate function implementation.", "category": 1, "code": 2393}, {"start": 7187, "length": 15, "code": 2412, "category": 1, "messageText": {"messageText": "Type 'Error | undefined' is not assignable to type 'Error' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.", "category": 1, "code": 2412, "next": [{"messageText": "Type 'undefined' is not assignable to type 'Error'.", "category": 1, "code": 2322}]}}, {"start": 8176, "length": 5, "messageText": "'error' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 15105, "length": 14, "code": 2345, "category": 1, "messageText": "Argument of type '\"beforeUnload\"' is not assignable to parameter of type 'keyof LifecycleHooks'."}, {"start": 15260, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'unload' does not exist on type 'LifecycleHooks'."}, {"start": 15306, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'unload' does not exist on type 'LifecycleHooks'."}, {"start": 15499, "length": 13, "code": 2322, "category": 1, "messageText": "Type 'null' is not assignable to type 'string | HTMLElement'."}, {"start": 15533, "length": 11, "code": 2322, "category": 1, "messageText": "Type 'null' is not assignable to type 'SandboxConfig'."}, {"start": 15565, "length": 13, "code": 2322, "category": 1, "messageText": "Type 'null' is not assignable to type 'LifecycleHooks'."}, {"start": 15694, "length": 13, "code": 2345, "category": 1, "messageText": "Argument of type '\"afterUnload\"' is not assignable to parameter of type 'keyof LifecycleHooks'."}, {"start": 16398, "length": 17, "messageText": "Duplicate function implementation.", "category": 1, "code": 2393}, {"start": 17006, "length": 6, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'AppStatusType' is not assignable to parameter of type '\"NOT_MOUNTED\" | \"SKIP_BECAUSE_BROKEN\"'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '\"NOT_LOADED\"' is not assignable to type '\"NOT_MOUNTED\" | \"SKIP_BECAUSE_BROKEN\"'.", "category": 1, "code": 2322}]}}]], [70, [{"start": 148, "length": 14, "messageText": "'\"../constants\"' has no exported member named 'RESOURCE_TYPES'. Did you mean 'RESOURCE_TYPE'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./src/constants.ts", "start": 3886, "length": 13, "messageText": "'RESOURCE_TYPE' is declared here.", "category": 3, "code": 2728}]}, {"start": 324, "length": 5, "messageText": "Module '\"../utils\"' has no exported member 'retry'.", "category": 1, "code": 2305}, {"start": 324, "length": 5, "messageText": "'retry' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 990, "length": 4, "code": 2345, "category": 1, "messageText": "Argument of type 'string' is not assignable to parameter of type 'ErrorCode'."}, {"start": 3876, "length": 6, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'loaded' does not exist in type 'ExtendedResourceInfo'."}, {"start": 4141, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'content' does not exist on type 'ResourceInfo'."}, {"start": 4175, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'loaded' does not exist on type 'ExtendedResourceInfo'."}, {"start": 4308, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'content' does not exist on type 'ResourceInfo'."}, {"start": 4404, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'content' does not exist on type 'ResourceInfo'."}, {"start": 5541, "length": 8, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | undefined' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 5702, "length": 8, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | undefined' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 6578, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'loaded' does not exist on type 'ResourceInfo'."}, {"start": 7475, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"script\" | \"style\" | \"html\" | \"image\" | \"font\" | \"other\"'.", "relatedInformation": [{"file": "./src/types/app.ts", "start": 3681, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'ResourceInfo'", "category": 3, "code": 6500}]}, {"start": 7917, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'content' does not exist on type 'ResourceInfo'."}, {"start": 7975, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'loaded' does not exist on type 'ResourceInfo'."}]], [71, [{"start": 702, "length": 6, "messageText": "'config' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1118, "length": 18, "messageText": "'memoryLeakDetector' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1183, "length": 12, "messageText": "'cleanupTasks' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1256, "length": 15, "messageText": "'_sandboxManager' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1281, "length": 28, "messageText": "Cannot find module '../sandbox/sandbox-manager' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1339, "length": 16, "messageText": "'_resourceManager' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1697, "length": 17, "messageText": "Expected 1 arguments, but got 0.", "category": 1, "code": 2554, "relatedInformation": [{"file": "./src/runtime/app-registry.ts", "start": 588, "length": 18, "messageText": "An argument for 'eventBus' was not provided.", "category": 3, "code": 6210}]}, {"start": 5540, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'clear' does not exist on type 'EventBus'."}, {"start": 9307, "length": 4, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'this' is not assignable to parameter of type 'PluginSystem'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'MicroCoreKernel' is missing the following properties from type 'PluginSystem': register, unregister, get", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type 'MicroCoreKernel' is not assignable to type 'PluginSystem'."}}]}}]], [72, [{"start": 718, "length": 23, "code": 2345, "category": 1, "messageText": "Argument of type '`\\u63D2\\u4EF6 ${string} \\u5DF2\\u5B58\\u5728`' is not assignable to parameter of type 'ErrorCode'."}, {"start": 759, "length": 11, "messageText": "Cannot find name 'ERROR_CODES'.", "category": 1, "code": 2304}, {"start": 1258, "length": 16, "code": 2345, "category": 1, "messageText": "Argument of type '`\\u63D2\\u4EF6 ${string} \\u4E0D\\u5B58\\u5728`' is not assignable to parameter of type 'ErrorCode'."}, {"start": 1292, "length": 11, "messageText": "Cannot find name 'ERROR_CODES'.", "category": 1, "code": 2304}, {"start": 1445, "length": 9, "messageText": "Expected 1 arguments, but got 0.", "category": 1, "code": 2554, "relatedInformation": [{"file": "./src/types/common.ts", "start": 1342, "length": 26, "messageText": "An argument for 'pluginSystem' was not provided.", "category": 3, "code": 6210}]}, {"start": 4284, "length": 6, "messageText": "'plugin' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [74, [{"start": 97, "length": 10, "messageText": "'\"../errors\"' has no exported member named 'ErrorCodes'. Did you mean 'ErrorCode'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./src/errors.ts", "start": 2190, "length": 9, "messageText": "'ErrorCode' is declared here.", "category": 3, "code": 2728}]}, {"start": 2133, "length": 40, "code": 2345, "category": 1, "messageText": "Argument of type '`\\u6C99\\u7BB1 \"${string}\" \\u672A\\u6FC0\\u6D3B\\uFF0C\\u65E0\\u6CD5\\u8BBE\\u7F6E\\u5168\\u5C40\\u53D8\\u91CF`' is not assignable to parameter of type 'ErrorCode'."}, {"start": 3024, "length": 32, "code": 2345, "category": 1, "messageText": "Argument of type '`\\u6C99\\u7BB1 \"${string}\" \\u505C\\u7528\\u5931\\u8D25`' is not assignable to parameter of type 'ErrorCode'."}, {"start": 3468, "length": 38, "code": 2345, "category": 1, "messageText": "Argument of type '`\\u6C99\\u7BB1 \"${string}\" \\u672A\\u6FC0\\u6D3B\\uFF0C\\u65E0\\u6CD5\\u6267\\u884C\\u4EE3\\u7801`' is not assignable to parameter of type 'ErrorCode'."}, {"start": 3926, "length": 39, "code": 2345, "category": 1, "messageText": "Argument of type '`\\u6C99\\u7BB1\\u4EE3\\u7801\\u6267\\u884C\\u5931\\u8D25: ${string}`' is not assignable to parameter of type 'ErrorCode'."}, {"start": 4706, "length": 40, "code": 2345, "category": 1, "messageText": "Argument of type '`\\u6C99\\u7BB1 \"${string}\" \\u672A\\u6FC0\\u6D3B\\uFF0C\\u65E0\\u6CD5\\u8BBE\\u7F6E\\u5168\\u5C40\\u53D8\\u91CF`' is not assignable to parameter of type 'ErrorCode'."}, {"start": 5167, "length": 40, "code": 2345, "category": 1, "messageText": "Argument of type '`\\u6C99\\u7BB1 \"${string}\" \\u672A\\u6FC0\\u6D3B\\uFF0C\\u65E0\\u6CD5\\u83B7\\u53D6\\u5168\\u5C40\\u53D8\\u91CF`' is not assignable to parameter of type 'ErrorCode'."}, {"start": 6035, "length": 3, "messageText": "'app' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [76, [{"start": 1577, "length": 10, "messageText": "Module '\"./utils\"' has no exported member 'isFunction'.", "category": 1, "code": 2305}, {"start": 1589, "length": 8, "messageText": "Module '\"./utils\"' has no exported member 'isObject'.", "category": 1, "code": 2305}, {"start": 1599, "length": 8, "messageText": "Module '\"./utils\"' has no exported member 'isString'.", "category": 1, "code": 2305}]], [244, [{"start": 470, "length": 15, "messageText": "Module '\"../types\"' has no exported member 'MicroCoreConfig'.", "category": 1, "code": 2305}]]], "affectedFilesPendingEmit": [243, 66, 73, 48, 49, 76, 75, 64, 67, 68, 244, 71, 69, 72, 70, 74, 245, 62, 51, 50, 52, 53, 54, 55, 61, 56, 57, 58, 59, 60, 246, 63], "emitSignatures": [48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 243, 244, 245, 246], "version": "5.8.3"}