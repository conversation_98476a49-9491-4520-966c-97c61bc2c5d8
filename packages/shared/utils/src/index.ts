/**
 * @fileoverview 共享工具函数库入口文件
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

// 导出类型检查工具
export * from './type-check';

// 导出对象操作工具
export * from './object';

// 导出字符串处理工具
export * from './string';

// 导出URL处理工具
export * from './url';

// 导出DOM操作工具
export * from './dom';

// 导出函数工具
export * from './function';

// 导出异步工具
export * from './async';

// 导出数组工具
export * from './array';

// 导出日期工具
export * from './date';

// 导出存储工具
export * from './storage';

// 导出环境检测工具
export * from './env';

// 导出日志工具
export * from './logger';

// 导出事件总线
export * from './event-bus';

// 导出性能监控工具
export * from './performance';

// 导出错误处理工具
export * from './error-handler';

// 导出框架检测工具
export * from './framework-detector';

// 导出安全验证工具
export * from './security-validator';

// 重新导出工具对象
import { arrayUtils } from './array';
import { asyncUtils } from './async';
import { dateUtils } from './date';
import { domUtils } from './dom';
import { envUtils } from './env';
import { eventBusUtils } from './event-bus';
import { functionUtils } from './function';
import { loggerUtils } from './logger';
import { objectUtils } from './object';
import { storageUtils } from './storage';
import { stringUtils } from './string';
import { typeUtils } from './type-check';
import { urlUtils } from './url';

/**
 * 所有工具函数的集合
 */
export const utils = {
    type: typeUtils,
    object: objectUtils,
    string: stringUtils,
    url: urlUtils,
    dom: domUtils,
    function: functionUtils,
    async: asyncUtils,
    array: arrayUtils,
    date: dateUtils,
    storage: storageUtils,
    env: envUtils,
    logger: loggerUtils,
    eventBus: eventBusUtils
};

/**
 * 默认导出
 */
export default utils;

// 兼容性导出（保持向后兼容）
export {
    arrayUtils, asyncUtils, dateUtils, domUtils, envUtils, eventBusUtils, functionUtils, loggerUtils, objectUtils, storageUtils, stringUtils, typeUtils, urlUtils
};

